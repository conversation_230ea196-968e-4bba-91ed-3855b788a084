import { getSession } from 'next-auth/react';

/**
 * Get the backend access token from NextAuth session
 */
export const getBackendAccessToken = async (): Promise<string | null> => {
  try {
    const session = await getSession();
    return session?.accessToken || null;
  } catch (error) {
    console.error('Error getting backend access token:', error);
    return null;
  }
};

/**
 * Get the backend refresh token from NextAuth session
 */
export const getBackendRefreshToken = async (): Promise<string | null> => {
  try {
    const session = await getSession();
    return session?.refreshToken || null;
  } catch (error) {
    console.error('Error getting backend refresh token:', error);
    return null;
  }
};

/**
 * Get both backend tokens from NextAuth session
 */
export const getBackendTokens = async (): Promise<{
  accessToken: string | null;
  refreshToken: string | null;
}> => {
  try {
    const session = await getSession();
    return {
      accessToken: session?.accessToken || null,
      refreshToken: session?.refreshToken || null,
    };
  } catch (error) {
    console.error('Error getting backend tokens:', error);
    return {
      accessToken: null,
      refreshToken: null,
    };
  }
};
