import axios from 'axios';
import {
  AddProductQuestionSuccessResponse,
  AddToCartRequest,
  AddToCartSuccessResponse,
  AddToWishlistRequest,
  AddressData,
  AddressValidationSuccessResponse,
  CancelOrderSuccessResponse,
  CompareSuccessResponse,
  CreateReviewRequest,
  CreateReviewResponse,
  CreateUserRequest,
  CreateUserSuccessResponse,
  DeleteAllCartItemsSuccessResponse,
  DeleteCartItemSuccessResponse,
  DeleteReviewListResponse,
  DeleteUserAddressSuccessResponse,
  GetAllBlogPostsSuccessResponse,
  GetAllProductsSuccessResponse,
  GetBlogSuccessResponse,
  GetCartSuccessResponse,
  GetCategoryBySlugSuccessResponse,
  GetCategoryListSuccessResponse,
  GetCategorySuccessResponse,
  // GetCustomerQuery,
  // GetCustomerResponse,
  GetUserAllProductsSuccessResponse,
  GetUserInformationSuccessResponse,
  GetUserProductByURLSuccessResponse,
  GetUserProductSuccessResponse,
  IPaymentMethodListSuccessRes,
  IPlaceOrderReq,
  IPlaceOrderRes,
  IProductSearchResponse,
  IReOrderQuery,
  IStripePaymentResponse,
  OrderByUserId,
  OrderByUserIdResponse,
  ProductQuestionsWithAnswerForUserSuccessResponse,
  ReviewListResponse,
  SendOtpForSignupRequest,
  SendOtpSuccessResponse,
  SubscriberResponse,
  SuccessResponse,
  UpdateCartItemRequest,
  UpdateCartItemSuccessResponse,
  UpdateUserAddressSuccessResponse,
  UpdateUserRequestBody,
  UpdateUserSuccessResponse,
  // GetCustomerErrorResponse,
  UserAddress,
  UserForgotPasswordRequest,
  UserForgotPasswordSuccessResponse,
  UserSignInRequest,
  UserSignInSuccessResponse,
  VerifyOtpRequest,
  VerifyOtpSuccessResponse,
  Wishlist,
} from 'models';
import { toast } from 'react-toastify';

import { apiEndPoints } from 'APIs/utils/apiEndPoints';
// import { User } from 'utils/types';
import {
  FileUploadRequestBody,
  PublicUploadFileSuccessResponse,
  UploadFileSuccessResponse,
} from 'models/media';
import { NextRouter } from 'next/router';

export async function signinRest(
  data: UserSignInRequest
): Promise<UserSignInSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.login}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function sendOTPRest(
  data: SendOtpForSignupRequest
): Promise<SuccessResponse | any> {
  // let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
  // const isEmail = regex.test(data);
  // const reqData = isEmail ? { email: data } : { phone: data };
  try {
    const res = await axios.post(`${apiEndPoints.sendOTP}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function signUpRest(
  data: CreateUserRequest
): Promise<CreateUserSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.register}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPublicProductsRest(): Promise<GetUserAllProductsSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.getPublicProducts}`);
    return res.data as GetUserAllProductsSuccessResponse;
  } catch (error: any) {
    return error;
  }
}

export async function getFeaturedProductsRest(): Promise<GetAllProductsSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getPublicProducts}?isFeatured=true`
    );
    return res.data.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPublicProductByIdRest(
  productId: string
): Promise<GetUserProductSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getPublicProducts}/${productId}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getCategoryListRest(): Promise<GetCategoryListSuccessResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.getCatagoryList}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}
export async function checkoutRest(
  data: IPlaceOrderReq,
  router: NextRouter
): Promise<IPlaceOrderRes> {
  try {
    const res = await axios.post(`${apiEndPoints.order}/create-order`, data);
    return res.data.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPublicProductByCategoryIDRest(
  categoryId: string,
  orderBy: string,
  minPrice: number,
  maxPrice: number,
  brands: string,
  skip: number,
  limit: number
): Promise<GetUserAllProductsSuccessResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getPublicProducts}?categoryId=${categoryId}${
        orderBy ? `&orderBy=${orderBy}` : ''
      }${brands ? `&brand=${brands}` : ''}${
        minPrice ? `&minPrice=${minPrice}` : ''
      }${maxPrice ? `&maxPrice=${maxPrice}` : ''}${
        skip ? `&offset=${skip}` : `&offset=${0}`
      }${limit ? `&limit=${limit}` : `&limit=${5}`}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}
export async function addToWishlistRest(
  data: AddToWishlistRequest
): Promise<SuccessResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.addToWishList}`, data);
    return res.data.data;
  } catch (error: any) {
    return error;
  }
}

export async function getOrderProductsRest(
  token: string
): Promise<OrderByUserIdResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.order}/list`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return res?.data?.data;
  } catch (error: any) {
    return [] as any;
  }
}

export async function getOrderProductRest(
  OrderId: string
): Promise<OrderByUserId> {
  try {
    const res = await axios.get(`${apiEndPoints.order}/${OrderId}`);
    return res?.data.data;
  } catch (error: any) {
    return [] as any;
  }
}

export async function addToCompareRest(
  productId: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.post(
      `${apiEndPoints.addToCompare}`,
      { productId },
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
    return res.data as CompareSuccessResponse;
  } catch (error: any) {
    return error;
  }
}

export async function getCustomerWishlistRest(
  token: string
): Promise<Wishlist | any> {
  try {
    const res = await axios.get(`${apiEndPoints.getCustomerWishlist}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res?.data?.data as Wishlist;
  } catch (error: any) {
    const errorData = {
      userId: '',
      id: '',
      items: [],
    };
    return errorData as any;
  }
}

export async function deleteWishlistItemRest(
  data: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.deleteWishlistItem}/${data}`
    );

    return res.data.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteFullWishlistRest(): Promise<SuccessResponse> {
  try {
    const res = await axios.delete(`${apiEndPoints.deleteFullWishlist}`);
    return res.data.message;
  } catch (error: any) {
    return error;
  }
}

export async function deleteFromCompareRest(
  productId: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.deleteFromCompare}?productId=${productId}`
    );
    return res.data as CompareSuccessResponse;
  } catch (error: any) {
    return error;
  }
}

export async function getCustomerProfileRest(
  token: string
): Promise<GetUserInformationSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.getCustomerProfile}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return res.data;
  } catch (error) {
    return {} as any;
  }
}

export async function addCustomerNewAddressRest(
  UserAddress: UserAddress
): Promise<SuccessResponse> {
  try {
    const res = await axios.put(`${apiEndPoints.addUserAddress}`, UserAddress);
    toast.success('New Address added', {
      containerId: 'bottom-right',
    });
    return res.data.data;
  } catch (error: any) {
    toast.error(error.message, {
      containerId: 'bottom-right',
    });
    return error;
  }
}

export async function deleteUserAddressRest(
  addressId: string
): Promise<DeleteUserAddressSuccessResponse | any> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.deleteUserAddress}/${addressId}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateUserAddressRest(
  addressId: string,
  data: UserAddress
): Promise<UpdateUserAddressSuccessResponse | any> {
  try {
    const res = await axios.patch(
      `${apiEndPoints.updateUserAddress}/${addressId}`,
      data
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getCustomerRest(
  token: string
): Promise<GetUserInformationSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.customer}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateCustomerRest(
  data: UpdateUserRequestBody
): Promise<UpdateUserSuccessResponse | any> {
  try {
    const response = await axios.patch(`${apiEndPoints.customer}`, data);
    return response.data;
  } catch (error: any) {
    return error;
  }
}

export async function getCartRest(token: string): Promise<GetCartSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.getCart}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function addToCartRest(
  data: AddToCartRequest
): Promise<AddToCartSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.addToCart}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteAllCartItemRest(): Promise<DeleteAllCartItemsSuccessResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.deleteAllCartItem}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function deleteSingleCartItemRest(
  productId: string
): Promise<DeleteCartItemSuccessResponse | any> {
  try {
    const res = await axios.delete(
      `${apiEndPoints.deleteSingleCartItem}?productId=${productId}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateCartItemRest(
  cartItem: UpdateCartItemRequest
): Promise<UpdateCartItemSuccessResponse | any> {
  try {
    const res = await axios.patch(`${apiEndPoints.updateCartItem}`, cartItem);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function forgetPasswordSendOtpRest(
  data: string
): Promise<SendOtpSuccessResponse | any> {
  let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
  const isEmail = regex.test(data);
  try {
    const res = await axios.post(
      `${apiEndPoints.forgetPasswordSendOtp}`,
      isEmail ? { email: data } : { phone: data }
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function forgetPasswordVerifyOtpRest(
  data: VerifyOtpRequest
): Promise<VerifyOtpSuccessResponse | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.forgetPasswordVerifyOtp}`,
      data
    );
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function resetPasswordRest(
  data: UserForgotPasswordRequest
): Promise<UserForgotPasswordSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.resetPassword}`, data);
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBrandsRest(): Promise<SuccessResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.brands}`);
    return res?.data;
  } catch (error: any) {
    return error;
  }
}

export async function getPublicProductByUniqueNameRest(
  productUniqueName: string
): Promise<SuccessResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getPublicProductByUniqueName}/${productUniqueName}`
    );
    return res.data.data as GetUserProductByURLSuccessResponse;
  } catch (error: any) {
    return error;
  }
}

export async function getCategoryDetailsByIdRest(
  categoryId: string
): Promise<GetCategorySuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getCategoryDetails}/${categoryId}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getCategoryDetailsBySlugRest(
  categorySlug: string
): Promise<GetCategoryBySlugSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getCategoryBySlug}/${categorySlug}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function searchProductsRest(
  searchText: string,
  pageNumber: number,
  limit: number
): Promise<IProductSearchResponse> {
  try {
    const res = await axios.get(
      `${apiEndPoints.search}/?q=${searchText}&filterBy=PRODUCT&offset=0${
        limit ? `&limit=${limit}` : ''
      }`
    );
    return res.data.data as IProductSearchResponse;
  } catch (error: any) {
    return error;
  }
}

export async function getCompareRest(): Promise<SuccessResponse> {
  try {
    const res = await axios.get(`${apiEndPoints.addToCompare}`);
    return res.data as CompareSuccessResponse;
  } catch (error: any) {
    return error;
  }
}

export async function reorderRest(
  data: IReOrderQuery
): Promise<SuccessResponse> {
  try {
    const res = await axios.post(`${apiEndPoints.order}/reorder`, data);
    return res.data;
  } catch (error: any) {
    toast.error('Something Went wrong on re-order', {
      containerId: 'bottom-right',
    });
    return error;
  }
}

export async function askQuestionAboutProductRest(
  productId: string,
  question: string
): Promise<AddProductQuestionSuccessResponse | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.getPublicProducts}/${productId}/add-question`,
      { question }
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getQuestionsAboutProductRest(
  productId: string,
  offset: number,
  limit: number
): Promise<ProductQuestionsWithAnswerForUserSuccessResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.getPublicProducts}/${productId}/questions-answers?offset=${offset}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function uploadPublicMediaRest(
  file: any,
  featureName: string
): Promise<PublicUploadFileSuccessResponse | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.publicMedia}`,
      {
        file,
        featureName,
      },
      {
        headers: {
          Accept: '*/*',
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createReviewRest(
  data: CreateReviewRequest
): Promise<CreateReviewResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.review}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getReviewRest(
  productId: string,
  skip?: number,
  limit?: number
): Promise<ReviewListResponse | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.review}/${productId}?skip=${skip}&limit=${limit}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function uploadPrivateMediaRest(
  data: FileUploadRequestBody
): Promise<UploadFileSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.privateMedia}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getUserPointsRest(): Promise<any> {
  try {
    const res = await axios.get(`${apiEndPoints.points}`);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function stripeRepayRest(
  orderId: string
): Promise<IStripePaymentResponse | any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.repay}/stripe/create-payment`,
      { orderId }
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getPaymentMethodsRest(): Promise<IPaymentMethodListSuccessRes | any> {
  try {
    const res = await axios.get(
      `${apiEndPoints.order}/available-payment-methods`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function createCheckoutSummaryRest(data: any): Promise<any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.order}/checkout-summary`,
      data
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function deleteReviewRest(
  reviewId: string
): Promise<DeleteReviewListResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.review}/${reviewId}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function updateReviewRest(
  data: CreateReviewRequest
): Promise<CreateReviewResponse | any> {
  try {
    const res = await axios.put(`${apiEndPoints.review}`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function joinWaitListRest(
  email: string
): Promise<SubscriberResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.waitlistjoin}`, { email });
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function validateAddressRest(
  data: AddressData
): Promise<AddressValidationSuccessResponse | any> {
  try {
    const res = await axios.post(`${apiEndPoints.customer}/validate-address`, data);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBlogsRest(skip?: number, limit?: number, token?: string, currentMonth?: string): Promise<GetAllBlogPostsSuccessResponse | any> {
  try {
    let res;
    if(currentMonth?.length! > 0) {
      res = await axios.get(`${apiEndPoints.blogs}?filter=${currentMonth}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } else {
    res = await axios.get(`${apiEndPoints.blogs}?offset=${skip}&limit=${limit}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getBlogByIdRest(id: string, token: string): Promise<GetBlogSuccessResponse | any> {
  try {
    const res = await axios.get(`${apiEndPoints.blogs}/${id}`, { headers: {
      Authorization: `Bearer ${token}`,
    }});
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function cancelOrderRest(id: string): Promise<CancelOrderSuccessResponse | any> {
  try {
    const res = await axios.delete(`${apiEndPoints.order}/cancel-order/{id}?orderId=${id}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getWpBlogsRest(page: number, limit: number): Promise<any> {
  try {
    const res = await axios.get(`${process?.env?.NEXT_PUBLIC_WORDPRESS_API_URL!}${apiEndPoints.wp_blogs}?_embed&order=desc&page=1&per_page=100&status=publish`);
    return res;
  } catch (error: any) {
    return error;
  }
}

export async function getWpSingleBlogRest(slug: string): Promise<any> {
  try {
    const res = await axios.get(`${process?.env?.NEXT_PUBLIC_WORDPRESS_API_URL!}${apiEndPoints.wp_blogs}?_embed&slug=${slug}`);
    return res.data;
  } catch (error: any) {
    return error;
  }
}


export async function getNewsFeedRest(
  token: string
): Promise<any> {
  try {
    const res = await axios.get(`${apiEndPoints.getNewsFeed}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data;
  } catch (error: any) {
    return error;
  }
}
export async function createPostRest(
  data: any
): Promise<any> {
  try {
    const res = await axios.post(`${apiEndPoints.createPost}`, data);
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function sendMultiReactionRest(
  postId: string,
  ownerId: string,
  reactionType: string,
  isReacting: boolean
): Promise<any> {
  try {
    const res = await axios.post(
      `${apiEndPoints.sendReaction}/${postId}/multireaction?ownerId=${ownerId}&reactionType=${reactionType}&isReacting=${isReacting}`
    );
    return res.data;
  } catch (error: any) {
    toast.error(error);
    return error;
  }
}

export async function getMyStoryRest(
  token: string
): Promise<any> {
  try {
   
    const res = await axios.get(`${apiEndPoints.myStory}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    
    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getFollowerStoriesRest(token: string): Promise<any> {
  try {
    
    const res = await axios.get(`${apiEndPoints.followerStories}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
   
    return res.data;
  } catch (error: any) {
    return error;
  }
}

