export const apiEndPoints = {
  getUser: `/todos`,
  // getSignedInUser: `/customer/auth`,
  getPublicProducts: '/user/products',
  getProducts: '/products',
  register: '/user-auth/signup/verify-otp',
  sendOTP: '/user-auth/signup/send-otp',
  verifyOTP: '/user/auth/register/verify-otp',
  login: '/user-auth/sign-in',
  forgetPasswordSendOtp: '/user-auth/forgot-password/send-otp',
  forgetPasswordVerifyOtp: '/user-auth/forgot-password/verify-otp',
  resetPassword: '/user-auth/forgot-password',
  getCart: `/cart`,
  addToCart: `/cart`,
  deleteSingleCartItem: `/cart/item`,
  deleteAllCartItem: '/cart/allitems',
  updateCartItem: `/cart/item`,
  getCatagoryList: `/categories`,
  order: `/user/orders`,
  addToWishList: `/wishlist`,
  getCustomerWishlist: `/wishlist`,
  deleteWishlistItem: `wishlist/items`,
  deleteFullWishlist: `/wishlist/allitems`,
  addToCompare: `/compare`,
  deleteFromCompare: `/compare/item`,
  getCustomerProfile: `/user`,
  addUserAddress: `/user/add-address`,
  updateUserAddress: `/user/update-address`,
  deleteUserAddress: `/user/delete-address`,
  customer: '/user',
  brands: '/brands',
  getPublicProductByUniqueName: '/user/products/url',
  getCategoryDetails: '/categories',
  getCategoryBySlug: '/categories/slug',
  search: '/search',
  publicMedia: '/media/public/upload/single',
  review: '/user/order/review',
  privateMedia: '/media/upload-presigned-url',
  points: '/points',
  repay: '/payment',
  waitlistjoin: '/public/subscribe',
  blogs: '/user/blogs',
  wp_blogs: '/posts',
  getNewsFeed: '/post/newsfeed?refresh=false',
  sendReaction:'post',
  createPost: '/post/create',
  createStore: '/story/share',
  myStory:'story/my-story',
  followerStories:'story/fetch-stories'
};
