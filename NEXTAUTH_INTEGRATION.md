# NextAuth.js Backend Integration

This document describes the NextAuth.js integration with the backend authentication system.

## Overview

The integration allows users to sign in with Google or Facebook through NextAuth.js, which then automatically calls the backend authentication endpoints and stores the backend JWT tokens in the NextAuth session.

## Configuration

### Environment Variables

The following environment variables are configured in `.env.local`:

```
NEXTAUTH_URL=http://localhost:4003
NEXT_PUBLIC_API_PREFIX_REST=http://localhost:3000/api

GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_CLIENT_ID=your_facebook_client_id
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
NEXTAUTH_SECRET=your_nextauth_secret
```

### Backend Endpoints

The integration calls these backend endpoints:
- `POST http://localhost:3000/api/user-auth/google/sign-in`
- `POST http://localhost:3000/api/user-auth/facebook/sign-in`

Both endpoints expect:
```json
{
  "accessToken": "oauth_access_token_from_provider"
}
```

And return:
```json
{
  "data": {
    "accessToken": "backend_jwt_access_token",
    "refreshToken": "backend_jwt_refresh_token",
    "user": {
      "id": "user_id",
      "name": "user_name",
      "email": "user_email",
      "image": {
        "profile": "profile_image_url"
      }
    }
  }
}
```

## How It Works

1. **User clicks social login button** in the sign-in component
2. **NextAuth handles OAuth flow** with Google/Facebook
3. **NextAuth receives OAuth access token** from the provider
4. **JWT callback intercepts the token** and calls the backend endpoint
5. **Backend validates OAuth token** and returns JWT tokens
6. **NextAuth stores backend tokens** in the session
7. **Frontend can access tokens** via `session.accessToken` and `session.refreshToken`

## Files Modified

### Core Configuration
- `pages/api/auth/[...nextauth].ts` - NextAuth configuration with backend integration
- `types/next-auth.d.ts` - TypeScript type definitions for extended session
- `.env.local` - Environment configuration

### Components
- `modules/account/signIn/SocialLogin/SocialLogin.tsx` - Social login buttons
- `modules/account/signIn/index.tsx` - Updated to include social login

### Utilities
- `utils/auth.ts` - Helper functions to access backend tokens from session

### Testing
- `pages/auth-test.tsx` - Test page to verify authentication flow
- `pages/api/test-backend.ts` - API endpoint to test backend connectivity

## Testing the Integration

### Prerequisites
1. **Backend server running** on `http://localhost:3000`
2. **Frontend server running** on `http://localhost:4003`
3. **Valid OAuth credentials** configured in environment variables

### Test Steps

1. **Check backend connectivity**:
   ```bash
   curl http://localhost:3000/api/health
   ```

2. **Visit the test page**:
   ```
   http://localhost:4003/auth-test
   ```

3. **Test social login**:
   - Go to `http://localhost:4003/account/sign-in`
   - Click "Continue with Google" or "Continue with Facebook"
   - Complete OAuth flow
   - Should redirect to `/post/newsfeed` with authenticated session

4. **Verify tokens**:
   - Visit `http://localhost:4003/auth-test` after login
   - Check that both NextAuth session and backend tokens are present
   - Verify user information is correctly populated

### Expected Behavior

✅ **Successful Integration**:
- Backend status shows "Connected"
- Social login buttons work without errors
- After OAuth, user is redirected with valid session
- Session contains `accessToken` and `refreshToken` from backend
- User information is populated from backend response

❌ **Common Issues**:
- Backend not running → Social login will fail silently
- Invalid OAuth credentials → OAuth flow will fail
- Network issues → Backend calls will timeout
- CORS issues → Backend calls will be blocked

## Session Structure

After successful authentication, the NextAuth session will contain:

```typescript
{
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
  accessToken: string;    // Backend JWT access token
  refreshToken: string;   // Backend JWT refresh token
  expires: string;
}
```

## Usage in Components

```typescript
import { useSession } from 'next-auth/react';
import { getBackendTokens } from 'utils/auth';

// In a component
const { data: session } = useSession();
const backendAccessToken = session?.accessToken;

// Or using the utility
const { accessToken, refreshToken } = await getBackendTokens();
```

## Security Considerations

- Backend JWT tokens are stored in NextAuth session (encrypted)
- OAuth access tokens are not persisted after backend call
- Session expires based on NextAuth configuration
- Backend handles token validation and single-session policy

## Troubleshooting

1. **Check backend logs** for authentication errors
2. **Verify OAuth credentials** are correctly configured
3. **Test backend endpoints** directly with curl/Postman
4. **Check browser network tab** for failed requests
5. **Use auth-test page** to debug session state
