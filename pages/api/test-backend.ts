import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Test if backend is accessible
    const response = await fetch('http://localhost:3000/api/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      res.status(200).json({
        status: 'success',
        message: 'Backend is accessible',
        backendResponse: data,
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: 'Backend is not accessible',
        statusCode: response.status,
      });
    }
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Failed to connect to backend',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
