import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import CredentialsProvider from 'next-auth/providers/credentials';
const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, FACEBOOK_CLIENT_ID, FACEBOOK_CLIENT_SECRET, NEXTAUTH_SECRET } =
  process?.env;

export default NextAuth({
  providers: [
    CredentialsProvider({
      id: 'google',
      name: 'Google',
      credentials: {
        accessToken: { label: 'Access Token', type: 'text' }
      },
      async authorize(credentials) {
        try {
          const response = await fetch('http://localhost:3000/api/user-auth/google/sign-in', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              accessToken: credentials?.accessToken,
            }),
          });

          const data = await response.json();

          if (response.ok && data.data) {
            return {
              id: data.data.user?.id || 'google-user',
              name: data.data.user?.name || null,
              email: data.data.user?.email || null,
              image: data.data.user?.image?.profile || null,
              accessToken: data.data.accessToken,
              refreshToken: data.data.refreshToken,
            };
          }
          return null;
        } catch (error) {
          console.error('Google sign-in error:', error);
          return null;
        }
      },
    }),
    CredentialsProvider({
      id: 'facebook',
      name: 'Facebook',
      credentials: {
        accessToken: { label: 'Access Token', type: 'text' }
      },
      async authorize(credentials) {
        try {
          const response = await fetch('http://localhost:3000/api/user-auth/facebook/sign-in', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              accessToken: credentials?.accessToken,
            }),
          });

          const data = await response.json();

          if (response.ok && data.data) {
            return {
              id: data.data.user?.id || 'facebook-user',
              name: data.data.user?.name || null,
              email: data.data.user?.email || null,
              image: data.data.user?.image?.profile || null,
              accessToken: data.data.accessToken,
              refreshToken: data.data.refreshToken,
            };
          }
          return null;
        } catch (error) {
          console.error('Facebook sign-in error:', error);
          return null;
        }
      },
    }),
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID!,
      clientSecret: GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    FacebookProvider({
      clientId: FACEBOOK_CLIENT_ID!,
      clientSecret: FACEBOOK_CLIENT_SECRET!,
      authorization: {
        params: {
          auth_type: 'reauthenticate'
        }
      }
    }),
  ],
  secret: NEXTAUTH_SECRET!,
  callbacks: {
    jwt: async ({ token, user, account }) => {
      // Handle OAuth providers (Google/Facebook)
      if (account && (account.provider === 'google' || account.provider === 'facebook')) {
        try {
          // Call our backend with the OAuth access token
          const backendUrl = account.provider === 'google'
            ? 'http://localhost:3000/api/user-auth/google/sign-in'
            : 'http://localhost:3000/api/user-auth/facebook/sign-in';

          const response = await fetch(backendUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              accessToken: account.access_token,
            }),
          });

          const data = await response.json();

          if (response.ok && data.data) {
            // Store backend tokens and user info
            token.accessToken = data.data.accessToken;
            token.refreshToken = data.data.refreshToken;
            token.user = {
              id: data.data.user?.id || user?.id || 'unknown',
              name: data.data.user?.name || user?.name,
              email: data.data.user?.email || user?.email,
              image: data.data.user?.image?.profile || user?.image,
            };
          }
        } catch (error) {
          console.error(`Backend ${account.provider} sign-in error:`, error);
          // Continue with OAuth user data if backend fails
          token.user = {
            id: user?.id || 'unknown',
            name: user?.name,
            email: user?.email,
            image: user?.image,
          };
        }
      }

      // Store backend tokens when user signs in via credentials
      if (user && user.accessToken) {
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token.user = {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
        };
      }

      return token;
    },
    session: async ({ session, token }) => {
      // Expose backend tokens to the session
      session.accessToken = token.accessToken;
      session.refreshToken = token.refreshToken;

      if (token.user) {
        session.user = token.user;
      }

      return session;
    },
  },
});
