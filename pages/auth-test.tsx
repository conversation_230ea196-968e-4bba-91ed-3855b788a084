import { useSession, signOut } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { getBackendTokens } from 'utils/auth';

const AuthTest = () => {
  const { data: session, status } = useSession();
  const [backendTokens, setBackendTokens] = useState<{
    accessToken: string | null;
    refreshToken: string | null;
  }>({ accessToken: null, refreshToken: null });
  const [backendStatus, setBackendStatus] = useState<string>('checking...');

  useEffect(() => {
    const fetchTokens = async () => {
      const tokens = await getBackendTokens();
      setBackendTokens(tokens);
    };

    const checkBackend = async () => {
      try {
        const response = await fetch('/api/test-backend');
        const data = await response.json();
        setBackendStatus(data.status === 'success' ? 'connected' : 'disconnected');
      } catch (error) {
        setBackendStatus('disconnected');
      }
    };

    checkBackend();

    if (session) {
      fetchTokens();
    }
  }, [session]);

  if (status === 'loading') {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Test Page</h1>

        <div className="mb-6 bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium mb-2">Backend Status:</h3>
          <p className={`font-semibold ${
            backendStatus === 'connected' ? 'text-green-600' :
            backendStatus === 'disconnected' ? 'text-red-600' : 'text-yellow-600'
          }`}>
            {backendStatus === 'connected' ? '✅ Backend Connected' :
             backendStatus === 'disconnected' ? '❌ Backend Disconnected' : '⏳ Checking...'}
          </p>
        </div>
        
        {session ? (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-4 text-green-600">
              ✅ Authenticated
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">NextAuth Session:</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Backend Tokens:</h3>
                <div className="bg-gray-100 p-4 rounded">
                  <p><strong>Access Token:</strong> {backendTokens.accessToken ? '✅ Present' : '❌ Missing'}</p>
                  <p><strong>Refresh Token:</strong> {backendTokens.refreshToken ? '✅ Present' : '❌ Missing'}</p>
                  {backendTokens.accessToken && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-blue-600">Show Access Token</summary>
                      <pre className="mt-2 text-xs break-all">{backendTokens.accessToken}</pre>
                    </details>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">User Information:</h3>
                <div className="bg-gray-100 p-4 rounded">
                  <p><strong>ID:</strong> {session.user?.id || 'N/A'}</p>
                  <p><strong>Name:</strong> {session.user?.name || 'N/A'}</p>
                  <p><strong>Email:</strong> {session.user?.email || 'N/A'}</p>
                  <p><strong>Image:</strong> {session.user?.image || 'N/A'}</p>
                </div>
              </div>
              
              <button
                onClick={() => signOut({ callbackUrl: '/account/sign-in' })}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                Sign Out
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-4 text-red-600">
              ❌ Not Authenticated
            </h2>
            <p className="mb-4">You are not signed in.</p>
            <a
              href="/account/sign-in"
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Go to Sign In
            </a>
          </div>
        )}
        
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">Test Instructions:</h3>
          <ol className="list-decimal list-inside space-y-2">
            <li>Go to the sign-in page and try logging in with Google or Facebook</li>
            <li>After successful authentication, you should be redirected back here</li>
            <li>Check that both NextAuth session and backend tokens are present</li>
            <li>Verify that user information is correctly populated</li>
            <li>Test sign out functionality</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AuthTest;
