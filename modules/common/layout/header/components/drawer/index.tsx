import { useRouter } from 'next/router';
import { toast } from 'react-toastify';

import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { storeUserToken } from 'store/slices/authSlice';

import ChevronLeft from '@/modules/common/icons/chevronLeft';
import MapPinOutlineIcon from '@/modules/common/icons/mapPinIcon';
import ShoppingBagOutlineIcon from '@/modules/common/icons/shoppingBagIcon';
import UserOutlineIcon from '@/modules/common/icons/userIcon';
import { signOut } from 'next-auth/react';
import { resetCart } from 'store/slices/cartSlice';
import { resetAddress } from 'store/slices/customerAddressSlice';
import { resetWishilist } from 'store/slices/productsSlice';
import { storeProvider } from 'store/slices/providerSlice';
import { resetUserDetails } from 'store/slices/userSlice';
import Image from 'next/image';
import myImageLoader from 'image/loader';

interface Props {
  drawer: boolean;
  closeDrawer: () => void;
}

const Drawer: React.FC<Props> = ({ drawer, closeDrawer }: Props) => {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const customer = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const user: string = customer?.name
    ? customer?.name
    : customer?.email
    ? customer?.email
    : '';

  const getUsername = (name: string): string => {
    let computedName = '';
    const length = name.length;
    if (length > 20) {
      computedName = name.slice(0, 19);
      computedName += '...';
    } else computedName = name;

    return computedName;
  };

  const handleButtonClick = (path: string) => {
    closeDrawer();
    router.push(path);
  };

  const handleAuthState = () => {
    closeDrawer();
    if (token) {
      localStorage.clear();
      dispatch(resetAddress());
      dispatch(resetUserDetails());
      dispatch(resetWishilist());
      dispatch(resetCart());
      dispatch(storeUserToken(''));
      dispatch(storeProvider('none'));
      if (providerName !== 'none') {
        signOut({ callbackUrl: '/account/sign-in' });
      }
      router.push('/account/sign-in');
      toast.error('Logged out successfully!', {
        containerId: 'bottom-right',
      });
    } else {
      router.push('/account/sign-in');
    }
  };

  return (
    <div
      className={`fixed top-0 z-50 flex h-full w-full flex-col justify-between bg-white bg-cover bg-no-repeat px-8 py-4 transition duration-200 ease-in lg:hidden ${
        drawer ? 'translate-x-0' : '-translate-x-full'
      }`}
    >
      <div className="">
        <div className="flex items-center gap-2">
          <button
            className=" text-primary dark:text-dark_primary"
            onClick={() => closeDrawer()}
          >
            <ChevronLeft />
          </button>
          <Image
            src="/fitsomnia-icon.png"
            height={57}
            width={57}
            alt="Fitsomnia Logo"
            loader={myImageLoader}
            quality={100}
          />
          <p className="text-2xl font-medium text-primary dark:text-dark_primary">
            Marketplace by Fitsomnia
          </p>
        </div>
        <div className="font-medium">
          <div className="my-5 border-b border-t py-4">
            <div
              className="mb-3 flex flex-row items-center"
              onClick={() => handleButtonClick('/myAccount')}
            >
              <div className=" mr-2 inline">
                <UserOutlineIcon />
              </div>
              <button className="flex flex-col">
                <span>My Account</span>
                <span>{getUsername(user)}</span>
              </button>
            </div>

            <button
              className="flex"
              onClick={() => handleButtonClick('/order')}
            >
              <ShoppingBagOutlineIcon />
              <span className="ml-2">My Order</span>
            </button>
          </div>

          <button
            className="mb-4 flex"
            onClick={() => handleButtonClick('/myAccount/addresses')}
          >
            <MapPinOutlineIcon />
            <span className="ml-2">Address</span>
          </button>

          {/* <button className="flex">
            <GearOutlineIcon />
            <span className="ml-2">Settings</span>
          </button> */}
        </div>
      </div>
      <button
        className="w-full rounded-full border-2 border-primary py-2 text-primary dark:text-dark_primary"
        onClick={() => handleAuthState()}
      >
        {token ? 'Logout' : 'Login'}
      </button>
    </div>
  );
};

export default Drawer;
