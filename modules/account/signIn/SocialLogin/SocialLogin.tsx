import { signIn } from 'next-auth/react';
import { useAppDispatch } from 'store/hooks/index';
import { storeProvider } from 'store/slices/providerSlice';
import GoogleLogo from '@/modules/common/icons/googleLogo';
import FacebookLogo from '@/modules/common/icons/facebookLogo';
import { toast } from 'react-toastify';

interface Props {
  setLoader: (loading: boolean) => void;
}

const SocialLogin: React.FC<Props> = ({ setLoader }) => {
  const dispatch = useAppDispatch();

  const handleGoogleSignIn = async () => {
    try {
      setLoader(true);
      dispatch(storeProvider('google'));

      // Use NextAuth to handle Google OAuth
      await signIn('google', { callbackUrl: '/post/newsfeed' });
    } catch (error) {
      toast.error('Google sign-in failed', {
        containerId: 'bottom-right',
      });
      setLoader(false);
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      setLoader(true);
      dispatch(storeProvider('facebook'));

      // Use NextAuth to handle Facebook OAuth
      await signIn('facebook', { callbackUrl: '/post/newsfeed' });
    } catch (error) {
      toast.error('Facebook sign-in failed', {
        containerId: 'bottom-right',
      });
      setLoader(false);
    }
  };

  return (
    <div className="mt-6">
      <div className="mb-4">
        <p className="text-center text-gray-600">Or continue with</p>
      </div>
      
      <div className="flex flex-col gap-3">
        <button
          type="button"
          className="flex w-full items-center justify-center gap-3 rounded-lg border-2 border-gray-300 bg-white py-3 px-4 text-gray-700 transition-all duration-300 hover:border-gray-400 hover:bg-gray-50"
          onClick={handleGoogleSignIn}
        >
          <GoogleLogo />
          <span className="text-sm font-medium">Continue with Google</span>
        </button>
        
        <button
          type="button"
          className="flex w-full items-center justify-center gap-3 rounded-lg border-2 border-gray-300 bg-white py-3 px-4 text-gray-700 transition-all duration-300 hover:border-gray-400 hover:bg-gray-50"
          onClick={handleFacebookSignIn}
        >
          <FacebookLogo />
          <span className="text-sm font-medium">Continue with Facebook</span>
        </button>
      </div>
    </div>
  );
};

export default SocialLogin;
