import { ErrorMessage, Field, Form, Formik } from 'formik';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React, { useState } from 'react';

import { loginSchema } from '@/modules/account/schemas/loginSchema';
import { userAPI } from 'APIs';
import { UserSignInRequest } from 'models';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { useAppDispatch } from 'store/hooks/index';
import { storeUserToken } from 'store/slices/authSlice';
import { storeAllCartItems } from 'store/slices/cartSlice';
import { storeCompare } from 'store/slices/compareSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeWishlist } from 'store/slices/productsSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';

interface Props {
  setLoader: Function;
}

export const SignInForm: React.FC<Props> = ({ setLoader }) => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  let username = '';
  let loggedInUsingEmail = false;

  const fetchWislist = async (token: string) => {
    const wishlistedProducts = await userAPI.getCustomerWishlist(token);
    dispatch(storeWishlist(wishlistedProducts!));
  };

  const fetchCart = async (token: string) => {
    try {
      const cartProducts = await userAPI.getCart(token);
      if ('data' in cartProducts)
        dispatch(storeAllCartItems(cartProducts?.data?.items!));
      else {
        toast.error(cartProducts?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const fetchCompare = async () => {
    const compareProducts = await userAPI.getCompare();
    if ('data' in compareProducts!)
      dispatch(storeCompare(compareProducts?.data!));
  };

  async function handleSignin(data: UserSignInRequest) {
    try {
      setLoader(true);
      const token = await fetch('/api/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      const res = await token.json();
      if ('data' in res!) {
        dispatch(storeUserToken(res.data.token));
        await userAPI.getCustomer(res?.data?.token).then((response) => {
          dispatch(storeCustomerDetails(response?.data));
          dispatch(storeAddresses(response?.data?.addresses!));
        });
        await fetchCart(res?.data?.token);
        await fetchWislist(res?.data?.token);
        await fetchCompare();
        router.push('/post/newsfeed');
        toast.success('Logged in successfully!', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error('Something went wrong', {
          containerId: 'bottom-right',
        });
      }
    } catch (err) {
      toast.error('Invalid username or password.', {
        containerId: 'bottom-right',
      });
    } finally {
      setLoader(false);
    }
  }
  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            email: '',
            phone: '',
            username: '',
            password: '',
          }}
          onSubmit={(values, actions) => {
            let data;
            let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
            const isEmail = regex.test(values.username);
            username = values.username;
            isEmail
              ? (loggedInUsingEmail = true)
              : (loggedInUsingEmail = false);
            isEmail
              ? (data = {
                  email: values.username,
                  password: values.password,
                })
              : (data = {
                  phone: values.username,
                  password: values.password,
                });
            handleSignin(data);
            actions.setSubmitting(false);
          }}
          validationSchema={loginSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="text"
                      className="w-full rounded-lg border-2 p-2 placeholder-gray-600 outline-0"
                      id="username"
                      name="username"
                      placeholder="Email"
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="username" />
                    </div>
                  </div>
                </>

                <>
                  {/* <div className="mb-4">
                    <Field
                      type="password"
                      className="w-full rounded-lg border-2 p-2 placeholder-gray-600 outline-0"
                      id="password"
                      name="password"
                      placeholder="Password"
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="password" />
                    </div>
                  </div> */}

                  <div className="relative mb-4">
                    <Field
                      type={passwordVisible ? 'text' : 'password'}
                      className="w-full rounded-lg border-2 p-2 pr-10 placeholder-gray-600 outline-0"
                      id="password"
                      name="password"
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-3 text-primary"
                      onClick={() => setPasswordVisible(!passwordVisible)}
                    >
                      {passwordVisible ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M13.875 18.825A10.582 10.582 0 0112 19c-4.5 0-8.5-3.5-10-7 1.5-3.5 5.5-7 10-7 1.25 0 2.5.25 3.625.7M9.75 14.25l4.5-4.5M3 3l18 18"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 4c5 0 9 3.5 10 7-1 3.5-5 7-10 7-5 0-9-3.5-10-7 1-3.5 5-7 10-7zm0 3.5a3.5 3.5 0 110 7 3.5 3.5 0 010-7z"
                          />
                        </svg>
                      )}
                    </button>
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="password" />
                    </div>
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className="my-2 w-full rounded-full bg-primary py-3 text-base capitalize text-white  transition-all duration-500 ease-in-out  hover:bg-black dark:bg-dark_primary"
                  >
                    {/* {t('login:signin')} */}
                    Log In
                  </button>
                </div>
                <div className="flex flex-wrap justify-end">
                  <div
                    id="forgotPasswordDiv"
                    className="my-3 ml-auto text-gray-600 hover:text-gray-500"
                  >
                    <Link prefetch={false} href="/account/forgot-password">
                      {/* {t('login:forgot_password')} */}
                      Forgot password?
                    </Link>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
