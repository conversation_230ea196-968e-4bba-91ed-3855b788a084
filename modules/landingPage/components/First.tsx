import Logo from '@/modules/landingPage/assets/landing/logo.png';
import { navLinks } from '@/modules/landingPage/constant/Constant';
import Image from 'next/image';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { useRouter } from 'next/router';
import { handleLogout } from 'helper/handleLogout';
// import { HiBars3 } from "react-icons/hi2";

export const HeaderPage = () => {
  const dispatch = useAppDispatch();
    const router = useRouter();
    const token = useAppSelector(
      (state) => state.persistedReducer.auth.access_token
  );
    const providerName = useAppSelector(
      (state) => state?.persistedReducer?.provider?.provider
    );
  
  return (
    <header className="sticky top-0 z-50 bg-white">
      <div className="mt-4 h-[12vh] w-full ">
        <div className="mx-auto flex h-full w-[90%] items-center justify-between xl:w-[80%]">
          <div>
            <Link href="/" className=" flex items-center justify-between gap-2">
              <Image src={Logo} alt="logo image" width={39} height={39} />
              <span className="text-xl font-semibold text-primary">
                FITSOMNIA
              </span>
            </Link>
          </div>

          <div className="hidden w-full items-center justify-between md:flex md:w-auto">
            <ul className="flex flex-col p-4 md:flex-row md:space-x-8 md:border-0  md:p-0">
              {navLinks.map((item) => {
                return (
                  <li key={item.label}>
                    <Link href={item.link}>{item.label}</Link>
                  </li>
                );
              })}
            </ul>
          </div>

          <div className="flex items-center space-x-4">
            {/* <a
              type="button"
              href="#app-overview"
              className="align-items inline-flex justify-center rounded-full bg-[#25D366] px-4 py-2 font-medium text-white"
            >
              Download
            </a> */}
            {!token ? (
              <p className="align-items inline-flex justify-center rounded-full bg-[#25D366] px-4 py-2 font-medium text-white">
                <Link
                  prefetch={false}
                  data-testid="login-account-link"
                  href="/account/sign-in"
                  // className="text-primary underline"
                >
                  Log In
                </Link>
                /
                <Link
                  prefetch={false}
                  data-testid="login-account-link"
                  href="/account/sign-up"
                  // className="text-primary underline"
                >
                  Registration
                </Link>
              </p>
            ) : (
              <a
                onClick={() =>
                  handleLogout(localStorage, dispatch, providerName, router)
                }
                className="  cursor-pointer transition-all duration-100 ease-linear align-items inline-flex justify-center rounded-full bg-[#25D366] px-4 py-2 font-medium text-white"
              >
                logout
              </a>
            )}

            {/* <HiBars3 className="size-8 cursor-pointer text-black md:hidden" /> */}
          </div>
        </div>
      </div>
    </header>
  );
};
